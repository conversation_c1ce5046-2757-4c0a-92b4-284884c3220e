<?php
/**
 * Test script to verify the Share_model SQL fix
 * Place this in the root directory and access via browser
 * DELETE THIS FILE AFTER TESTING
 */

// Include CodeIgniter bootstrap
define('BASEPATH', TRUE);
require_once 'index.php';

echo "<h1>Share Model SQL Fix Test</h1>";

// Test 1: Check if Share_model can be loaded
echo "<h2>1. Testing Share_model Loading</h2>";
try {
    $CI =& get_instance();
    $CI->load->model('Share_model');
    echo "✓ Share_model loaded successfully<br>";
} catch (Exception $e) {
    echo "✗ Error loading Share_model: " . $e->getMessage() . "<br>";
    exit;
}

// Test 2: Test the get_all_shared_entities method with a small limit
echo "<h2>2. Testing get_all_shared_entities Method</h2>";
try {
    $result = $CI->Share_model->get_all_shared_entities(5, 0, null);
    echo "✓ get_all_shared_entities executed successfully<br>";
    echo "📊 Returned " . count($result) . " records<br>";
    
    if (!empty($result)) {
        echo "<strong>Sample record:</strong><br>";
        $sample = $result[0];
        echo "- Entity Type: " . $sample->entity_type . "<br>";
        echo "- Entity ID: " . $sample->entity_id . "<br>";
        echo "- Shared Date: " . $sample->shared_date . "<br>";
        
        // Check if entity names are properly retrieved
        switch($sample->entity_type) {
            case 'document':
                echo "- Document Name: " . ($sample->document_name ?? 'NULL') . "<br>";
                break;
            case 'deviation':
                echo "- Deviation Name: " . ($sample->deviation_name ?? 'NULL') . "<br>";
                break;
            case 'eventanalysis':
                echo "- Event Analysis Name: " . ($sample->eventanalysis_name ?? 'NULL') . "<br>";
                break;
            case 'riskassessment':
                echo "- Risk Assessment Name: " . ($sample->riskassessment_name ?? 'NULL') . "<br>";
                break;
        }
    }
} catch (Exception $e) {
    echo "✗ Error executing get_all_shared_entities: " . $e->getMessage() . "<br>";
    echo "📋 Last query: " . $CI->db->last_query() . "<br>";
}

// Test 3: Test with search parameter
echo "<h2>3. Testing Search Functionality</h2>";
try {
    $result = $CI->Share_model->get_all_shared_entities(5, 0, 'test');
    echo "✓ Search functionality executed successfully<br>";
    echo "📊 Search returned " . count($result) . " records<br>";
} catch (Exception $e) {
    echo "✗ Error executing search: " . $e->getMessage() . "<br>";
    echo "📋 Last query: " . $CI->db->last_query() . "<br>";
}

// Test 4: Test sharing statistics
echo "<h2>4. Testing Sharing Statistics</h2>";
try {
    $stats = $CI->Share_model->get_sharing_stats();
    echo "✓ get_sharing_stats executed successfully<br>";
    echo "📊 Statistics:<br>";
    foreach ($stats as $key => $value) {
        echo "- {$key}: {$value}<br>";
    }
} catch (Exception $e) {
    echo "✗ Error executing get_sharing_stats: " . $e->getMessage() . "<br>";
}

echo "<h2>Testing Complete</h2>";
echo "<p><strong>If all tests passed, the SQL syntax error has been fixed!</strong></p>";
echo "<p>Remember to delete this test file after verification.</p>";
?>
