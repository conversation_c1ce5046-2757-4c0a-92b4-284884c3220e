<?php
/**
 * Debug file to show recent log entries
 * DELETE THIS FILE AFTER TESTING
 */

$log_path = 'application/logs/';
if (is_dir($log_path)) {
    $log_files = glob($log_path . 'log-*.php');
    if (!empty($log_files)) {
        $latest_log = max($log_files);
        $content = file_get_contents($latest_log);
        
        // Extract recent entries related to share tracking
        $lines = explode("\n", $content);
        $recent_lines = array_slice($lines, -50); // Last 50 lines
        
        $share_related = array();
        foreach ($recent_lines as $line) {
            if (strpos($line, 'Track share') !== false || 
                strpos($line, 'Share') !== false || 
                strpos($line, 'shares') !== false) {
                $share_related[] = $line;
            }
        }
        
        if (!empty($share_related)) {
            echo "<strong>Share-related log entries:</strong><br>";
            foreach ($share_related as $line) {
                echo htmlspecialchars($line) . "<br>";
            }
        } else {
            echo "No share-related log entries found in recent logs.<br>";
            echo "<strong>Recent log entries (last 10):</strong><br>";
            $recent = array_slice($recent_lines, -10);
            foreach ($recent as $line) {
                if (trim($line)) {
                    echo htmlspecialchars($line) . "<br>";
                }
            }
        }
    } else {
        echo "No log files found";
    }
} else {
    echo "Log directory not found";
}
?>
