<?php
/**
 * Debug file to test share tracking functionality
 * Place this file in the root directory and access it via browser
 * DELETE THIS FILE AFTER TESTING
 */

// Include CodeIgniter bootstrap
require_once 'application/config/database.php';

echo "<h1>Share Tracking Debug</h1>";

// Test 1: Check if shares table exists
echo "<h2>1. Database Table Check</h2>";
try {
    $pdo = new PDO("mysql:host={$db['default']['hostname']};dbname={$db['default']['database']}", 
                   $db['default']['username'], $db['default']['password']);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $stmt = $pdo->query("SHOW TABLES LIKE 'shares'");
    if ($stmt->rowCount() > 0) {
        echo "✅ Table 'shares' exists<br>";
        
        // Check table structure
        $stmt = $pdo->query("DESCRIBE shares");
        echo "<strong>Table structure:</strong><br>";
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            echo "- {$row['Field']} ({$row['Type']})<br>";
        }
    } else {
        echo "❌ Table 'shares' does NOT exist<br>";
        echo "<strong>You need to run the SQL script first!</strong><br>";
    }
} catch (Exception $e) {
    echo "❌ Database connection error: " . $e->getMessage() . "<br>";
}

// Test 2: Check if there are any records
echo "<h2>2. Existing Records Check</h2>";
try {
    $stmt = $pdo->query("SELECT COUNT(*) as count FROM shares WHERE entity_type = 'document'");
    $result = $stmt->fetch(PDO::FETCH_ASSOC);
    echo "📊 Total document shares: " . $result['count'] . "<br>";
    
    if ($result['count'] > 0) {
        $stmt = $pdo->query("SELECT BIN_TO_UUID(share_id) as share_id, BIN_TO_UUID(entity_id) as entity_id, 
                            BIN_TO_UUID(shared_by) as shared_by, shared_date, entity_type 
                            FROM shares WHERE entity_type = 'document' ORDER BY shared_date DESC LIMIT 5");
        echo "<strong>Recent shares:</strong><br>";
        while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
            echo "- {$row['entity_type']}: {$row['entity_id']}, Shared: {$row['shared_date']}<br>";
        }
    }
} catch (Exception $e) {
    echo "❌ Error checking records: " . $e->getMessage() . "<br>";
}

// Test 3: Test AJAX endpoint
echo "<h2>3. AJAX Endpoint Test</h2>";
echo "<button onclick='testShareTracking()'>Test Share Tracking</button>";
echo "<div id='test-result'></div>";

// Test 4: Check logs
echo "<h2>4. Log Files</h2>";
$log_path = 'application/logs/';
if (is_dir($log_path)) {
    $log_files = glob($log_path . 'log-*.php');
    if (!empty($log_files)) {
        $latest_log = max($log_files);
        echo "📄 Latest log file: " . basename($latest_log) . "<br>";
        echo "<a href='#' onclick='showLogs()'>Show Recent Logs</a>";
        echo "<div id='logs' style='display:none; background:#f5f5f5; padding:10px; margin:10px 0; max-height:300px; overflow-y:scroll;'></div>";
    } else {
        echo "📄 No log files found<br>";
    }
} else {
    echo "📄 Log directory not found<br>";
}

?>

<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
function testShareTracking() {
    // Use a test document ID - you should replace this with a real document ID
    var testDocumentId = '37070108-aaaa-4f70-9bba-f52ef1c1e361'; // Replace with actual document ID
    
    $('#test-result').html('🔄 Testing...');
    
    $.ajax({
        url: '/documents/track_share',
        type: 'POST',
        data: {
            document_id: testDocumentId
        },
        dataType: 'json',
        success: function(response) {
            $('#test-result').html('✅ Success: ' + JSON.stringify(response));
        },
        error: function(xhr, status, error) {
            $('#test-result').html('❌ Error: ' + xhr.status + ' - ' + xhr.responseText);
        }
    });
}

function showLogs() {
    $.get('/debug_logs.php', function(data) {
        $('#logs').html(data).show();
    }).fail(function() {
        $('#logs').html('Could not load logs').show();
    });
}
</script>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
h1, h2 { color: #333; }
button { background: #007cba; color: white; padding: 8px 16px; border: none; cursor: pointer; }
button:hover { background: #005a87; }
</style>
