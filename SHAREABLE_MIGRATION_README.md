# Shareable Documents Migration to Generic Shares

This document describes the changes made to convert the document-specific sharing feature to a generic shares system that can handle multiple entity types.

## Changes Made

### 1. Database Schema Changes

**Old Structure (`document_shares` table):**
- `share_id` (binary 16, PK)
- `document_id` (binary 16)
- `shared_by` (binary 16)
- `shared_date` (datetime)
- `share_url` (varchar 500) - **REMOVED**
- `is_active` (tinyint 1) - **REMOVED**
- `created_at` (timestamp) - **REMOVED**
- `updated_at` (timestamp) - **REMOVED**

**New Structure (`shares` table):**
- `share_id` (binary 16, PK)
- `entity_type` (varchar 50) - **NEW**
- `entity_id` (binary 16) - **NEW** (replaces document_id)
- `shared_by` (binary 16)
- `shared_date` (datetime, default CURRENT_TIMESTAMP)

### 2. Database Configuration

**Updated:**
- `application/config/db_tables.php`: Changed `document_shares` to `shares`

### 3. Model Changes

**New Model:** `Share_model.php`
- Generic model that can handle any entity type
- Includes compatibility methods for existing document functionality
- Methods for documents: `is_document_shared()`, `get_document_share()`, etc.
- Generic methods: `is_entity_shared()`, `get_entity_share()`, etc.

**Deprecated Model:** `Document_share_model.php.deprecated`
- Old model renamed to indicate deprecation
- Can be removed after successful migration

### 4. Controller Changes

**Documents.php:**
- Updated to use `Share_model` instead of `Document_share_model`
- Removed `share_url` parameter handling
- Uses `add_share('document', $document_id)` instead of `add_share($document_id, $share_url)`

**admin/Documents.php:**
- Updated to use `Share_model` instead of `Document_share_model`
- Shareable documents admin functionality maintained

### 5. View Changes

**admin/documents/shareable.php:**
- Updated to generate share URLs dynamically instead of using stored URLs
- Removed references to `$doc->share_url`
- Uses `site_url('documentview/view/' . $doc->document_id)` for share links

**general/documents/view.php:**
- Updated `trackDocumentShare()` function to remove `share_url` parameter
- Simplified AJAX call

### 6. Debug Files Updated

**debug_share.php:**
- Updated to check for `shares` table instead of `document_shares`
- Updated test queries to use new schema
- Removed `share_url` from test AJAX call

**debug_logs.php:**
- Updated search patterns to look for `shares` instead of `document_shares`

### 7. SQL Migration Files

**New Files:**
- `sql/migrate_document_shares_to_shares.sql`: Migration script to convert existing data
- `sql/update_shareable_documents.sql`: Updated to create new `shares` table structure

## Migration Steps

1. **Backup Database**
   ```sql
   mysqldump -u username -p database_name document_shares > document_shares_backup.sql
   ```

2. **Run Migration**
   ```sql
   source sql/migrate_document_shares_to_shares.sql
   ```

3. **Verify Migration**
   - Check that all records were migrated correctly
   - Test sharing functionality
   - Verify admin interface works

4. **Clean Up** (after successful testing)
   ```sql
   DROP TABLE IF EXISTS document_shares;
   ```

## Benefits of New Structure

1. **Generic Design**: Can now support sharing of multiple entity types:
   - Documents
   - Deviations
   - Event Analysis
   - Risk Assessments

2. **Simplified Schema**: Removed unnecessary columns:
   - `share_url`: Generated dynamically as needed
   - `is_active`: Share exists or doesn't exist
   - `created_at`/`updated_at`: `shared_date` serves this purpose

3. **Unique Constraint**: Prevents duplicate shares of the same entity

4. **Future-Proof**: Easy to extend for new entity types

## Compatibility

The new `Share_model` includes compatibility methods to ensure existing code continues to work:
- `is_document_shared($document_id)`
- `get_document_share($document_id)`
- `update_document_share_date($document_id)`
- `remove_share($document_id)`

## Entity Types Supported

Currently configured for:
- `'document'` - Documents
- Future: `'deviation'`, `'event_analysis'`, `'risk_assessment'`

## Usage Examples

### Adding a Share
```php
$this->load->model('share_model');
$share_id = $this->share_model->add_share('document', $document_id);
```

### Checking if Entity is Shared
```php
$is_shared = $this->share_model->is_entity_shared('document', $document_id);
// Or use compatibility method:
$is_shared = $this->share_model->is_document_shared($document_id);
```

### Getting Share Information
```php
$share_info = $this->share_model->get_entity_share('document', $document_id);
```

### Getting All Shared Entities by Type
```php
$shared_documents = $this->share_model->get_shared_entities_by_type('document');
```
