<?php
/**
 * Debug script to check shares table content
 * DELETE THIS FILE AFTER TESTING
 */

// Include CodeIgniter bootstrap
define('BASEPATH', TRUE);
require_once 'index.php';

echo "<h1>Debug Shares Table</h1>";

try {
    $CI =& get_instance();
    $CI->load->model('Share_model');
    
    echo "<h2>1. Raw Shares Data</h2>";
    
    // Get raw data from shares table
    $CI->db->select('*');
    $CI->db->from('shares');
    $CI->db->order_by('shared_date', 'DESC');
    $CI->db->limit(10);
    $query = $CI->db->get();
    
    if ($query->num_rows() > 0) {
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Share ID</th><th>Entity Type</th><th>Entity ID</th><th>Shared By</th><th>Shared Date</th></tr>";
        
        foreach ($query->result() as $row) {
            $share_id = BIN_TO_UUID($row->share_id);
            $entity_id = BIN_TO_UUID($row->entity_id);
            $shared_by = BIN_TO_UUID($row->shared_by);
            
            echo "<tr>";
            echo "<td>" . htmlspecialchars($share_id) . "</td>";
            echo "<td>" . htmlspecialchars($row->entity_type) . "</td>";
            echo "<td>" . htmlspecialchars($entity_id) . "</td>";
            echo "<td>" . htmlspecialchars($shared_by) . "</td>";
            echo "<td>" . htmlspecialchars($row->shared_date) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No shares found in database.</p>";
    }
    
    echo "<h2>2. Entity Type Counts</h2>";
    $entity_types = ['document', 'deviation', 'eventanalysis', 'riskassessment'];
    
    foreach ($entity_types as $type) {
        $CI->db->where('entity_type', $type);
        $count = $CI->db->count_all_results('shares');
        echo "<p><strong>{$type}:</strong> {$count} shares</p>";
    }
    
    echo "<h2>3. Test get_all_shared_entities Method</h2>";
    
    $shared_entities = $CI->Share_model->get_all_shared_entities(10);
    
    if (!empty($shared_entities)) {
        echo "<p>Found " . count($shared_entities) . " shared entities:</p>";
        echo "<table border='1' style='border-collapse: collapse; width: 100%;'>";
        echo "<tr><th>Entity Type</th><th>Entity Name</th><th>Share ID</th><th>Shared By</th></tr>";
        
        foreach ($shared_entities as $entity) {
            echo "<tr>";
            echo "<td>" . htmlspecialchars($entity->entity_type) . "</td>";
            echo "<td>" . htmlspecialchars($entity->entity_name ?? 'N/A') . "</td>";
            echo "<td>" . htmlspecialchars($entity->share_id) . "</td>";
            echo "<td>" . htmlspecialchars($entity->shared_by_name ?? 'N/A') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>No shared entities returned from get_all_shared_entities()</p>";
    }
    
    echo "<h2>4. Test Deviation Name Retrieval</h2>";
    
    // Find a deviation share to test
    $CI->db->select('*');
    $CI->db->from('shares');
    $CI->db->where('entity_type', 'deviation');
    $CI->db->limit(1);
    $deviation_query = $CI->db->get();
    
    if ($deviation_query->num_rows() > 0) {
        $deviation_share = $deviation_query->row();
        $entity_id = BIN_TO_UUID($deviation_share->entity_id);
        
        echo "<p>Testing deviation name for ID: {$entity_id}</p>";
        
        // Test the simple method
        $deviation_name = $CI->Share_model->get_deviation_name_simple($entity_id);
        echo "<p>Deviation name: " . htmlspecialchars($deviation_name) . "</p>";
        
        // Check if deviation exists in deviation table
        $CI->db->select('*');
        $CI->db->from('deviation');
        $CI->db->where('a_id', UUID_TO_BIN($entity_id));
        $dev_query = $CI->db->get();
        
        if ($dev_query->num_rows() > 0) {
            echo "<p>✓ Deviation exists in deviation table</p>";
        } else {
            echo "<p>✗ Deviation NOT found in deviation table</p>";
        }
        
        // Check deviation_answers
        $CI->db->select('da.answer, df.title');
        $CI->db->from('deviation_answers da');
        $CI->db->join('deviation_fields df', 'da.df_id = df.df_id');
        $CI->db->where('da.a_id', UUID_TO_BIN($entity_id));
        $CI->db->where('df.input', 'input');
        $CI->db->where('df.required_kvalprak', 1);
        $answers_query = $CI->db->get();
        
        if ($answers_query->num_rows() > 0) {
            echo "<p>✓ Found deviation answers:</p>";
            foreach ($answers_query->result() as $answer) {
                echo "<p>- {$answer->title}: " . htmlspecialchars($answer->answer) . "</p>";
            }
        } else {
            echo "<p>✗ No deviation answers found</p>";
        }
        
    } else {
        echo "<p>No deviation shares found to test</p>";
    }
    
} catch (Exception $e) {
    echo "<p style='color: red;'>Error: " . htmlspecialchars($e->getMessage()) . "</p>";
}

echo "<h2>Debug Complete</h2>";
echo "<p><strong>Remember to delete this file after testing!</strong></p>";
?>
