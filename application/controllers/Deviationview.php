<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Deviationview extends MY_Controller
{

  public function __construct()
	{
		parent::__construct();
		$this->load->model(array('deviation_model'));
		$this->load->model(array('user_model'));
    $this->load->model(array('group_model'));
    $db = [
			'hostname' => $this->db->hostname,
			'database' => $this->db->database,
			'username' => $this->db->username,
			'password' => $this->db->password,
		];
		$this->load->library('deviationlib',$db);
    $this->load->helper(array('form','forms'));
		$this->load->helper('old_form');
    $this->auth_company_id  = $this->deviation_model->get_company_id();
		$this->users = $this->user_model->get_all();
  }
  public function view( $id )
	{
		// Check if this is a share_id instead of a deviation_id
		$this->load->model('Share_model');
		$share = $this->Share_model->get_share_by_id($id);
		
		if ($share && $share->entity_type === 'deviation') {
			// This is a share_id, use the actual entity_id
			$id = $share->entity_id;
		} else {
			// This could be a direct deviation_id access, verify share exists
			$share_exists = $this->Share_model->is_entity_shared('deviation', $id);
			if (!$share_exists) {
				show_404();
				return;
			}
		}

		$this->VALID_UUIDv4($id);

		$this->data['deviation']['registred'] = $this->deviation_model->deviationRegistredBy($id);
		if( empty($this->data['deviation']['registred']) ) { show_404(); }

    $this->data['deviation']['fields'] = $this->deviation_model->getDeviationPageAndOrId($id);
		if( empty($this->data['deviation']['fields']) ) { show_404(); }

		$this->data['getDeviationEventAnalysisMap']  = $this->deviationlib->getDeviationEventAnalysisMap($id);
		$this->data['getDeviationRiskAssesmentsMap'] = $this->deviationlib->getDeviationRiskAssesmentsMap($id);
		$this->data['getEventAnalysisNames']         = $this->deviationlib->getEventAnalysisNames($this->auth_company_id);
		$this->data['getRiskAssessmentsNames']       = $this->deviationlib->getRiskAssessmentsNames($this->auth_company_id);

		$this->data['a_id'] = $id;
		$this->data['attachments'] = $this->deviation_model->get_attachments($id);
		$this->data['fields']      = $this->deviation_model->getDeviationFieldsByPage(array(1,2,3));
		$this->data['options']     = $this->deviation_model->getDropdown();
		$this->data['groups']      = $this->group_model->get_all();
		$this->data['selected']    = [];

		$departments = $this->deviation_model->deviationDepartment($id);

		$this->_setOptions($this->data['fields'], $this->data['options'], $departments);
		$this->_setSelected($this->data['fields'], $this->data['selected'], $departments, $id);
		$this->load->view('general/deviation/viewpublic',$this->data);
	}

	private function _setOptions($fields, &$options, $departments)
	{
		if( ! empty($fields['type']['department']) )
		{
			foreach($fields['type']['department'] as $department)
			{
				foreach($departments as $d)
				{
					if( isset($this->data['groups']['department'][$d]) ) {
						$dropdown = new stdClass();
						$dropdown->option_id = $d;
						$dropdown->name      = $this->data['groups']['department'][$d];
						$options[$department][$d] = $dropdown;
					}
				}
			}
		}
		if( ! empty($fields['type']['email']) )
		{
			foreach($fields['type']['email'] as $email)
			{
				$emailId = $email;
				foreach($this->users as $user)
				{
					$dropdown = new stdClass();
					$dropdown->option_id = $user->user_id;
					$dropdown->name      = $user->name;
					$options[$email][$user->user_id] = $dropdown;
				}
			}
		}

		if( ! empty($fields['type']['users']) )
		{
			foreach($fields['type']['users'] as $users)
			{
				foreach($this->users as $user)
				{
					$dropdown = new stdClass();
					$dropdown->option_id = $user->user_id;
					$dropdown->name      = $user->name;
					$options[$users][$user->user_id] = $dropdown;
				}
			}
		}
		if( ! empty($fields['type']['eventanalysis']) )
		{
			foreach($fields['type']['eventanalysis'] as $eventanalysis)
			{
				$dropdown = new stdClass();
				$dropdown->option_id = 0;
				$dropdown->name      = 'Ingen djupare händelesanalys krävs';
				$options[$eventanalysis][0] = $dropdown;
				foreach($this->users as $user)
				{
					$dropdown = new stdClass();
					$dropdown->option_id = $eventanalysis . '_' . $user->user_id;
					$dropdown->name      = $user->name;
					$options[$eventanalysis][$user->user_id] = $dropdown;
				}
			}
		}
	}

	private function _setSelected($fields, &$selected, $departments, $id)
	{
		if( ! empty($fields['type']['department']) )
		{
			foreach($fields['type']['department'] as $department)
			{
				$selected[$department] = $departments;
			}
		}

		$emails = $this->deviation_model->get_email($id);

		if( ! empty($this->data['fields']['type']['email']) && ! empty($emails) )
		{
			foreach($this->data['fields']['type']['email'] as $email)
			{
				if( isset($emails[$email]) )
				{
					foreach($emails[$email] as $user_id)
					{
						if( ! isset($this->users[$user_id]) )
							continue;

						$selected[$email][] = $this->users[$user_id]->name;
					}
				}
			}
		}

	}

  public function download( $a_id )
	{
		$this->VALID_UUIDv4($a_id);
		$attachment = $this->deviation_model->get_attachment( $a_id );
		if( empty($attachment) ) { show_404(); }

		$deviation = $this->deviation_model->deviationRegistredBy($attachment->a_id);
		if( empty($deviation) ) { show_404(); }

		// @TODO: Check department

		$upload_base_path = CI_UPLOAD_PATH . 'deviation';
		$upload_path = $upload_base_path . DIRECTORY_SEPARATOR . $this->auth_company_id;
		$upload_file = $upload_path . DIRECTORY_SEPARATOR . $attachment->attachment_id . $attachment->file_ext;

		if( ! file_exists($upload_file) )
			show_404();

		if($attachment->file_ext === '.pdf')
		{
			$this->load->helper('pdf');
			pdf($attachment, $upload_file);
		}
		else
		{
			$this->load->helper('download');
			force_download($attachment->file_name, file_get_contents($upload_file), TRUE);
		}
	}
}
