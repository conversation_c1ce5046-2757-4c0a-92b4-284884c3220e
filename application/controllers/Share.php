<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Share extends MY_Controller {

	public function __construct()
	{
		parent::__construct();
		$this->load->model('share_model');
	}

	/**
	 * Track sharing of any entity type
	 * Accepts entity_type and entity_id parameters
	 */
	public function track()
	{
		if( ! $this->input->is_ajax_request() && $this->input->method(TRUE) !== 'POST' )
		{
			$this->output
					->set_status_header(400)
					->set_content_type('application/json', 'utf-8')
					->set_output(json_encode(['success' => FALSE, 'message' => 'Invalid request method'], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES))
					->_display();
			exit;
		}

		$entity_type = $this->input->post('entity_type');
		$entity_id = $this->input->post('entity_id');

		// Log for debugging
		log_message('debug', 'Track share called with entity_type: ' . $entity_type . ', entity_id: ' . $entity_id . ', user_id: ' . $this->auth_user_id);

		if( empty($entity_type) || empty($entity_id) )
		{
			log_message('error', 'Track share: Missing required parameters');
			$this->output
					->set_status_header(400)
					->set_content_type('application/json', 'utf-8')
					->set_output(json_encode(['success' => FALSE, 'message' => lang('share_missing_params')], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES))
					->_display();
			exit;
		}

		// Validate entity_type
		$allowed_types = ['document', 'deviation', 'eventanalysis', 'riskassessment'];
		if( ! in_array($entity_type, $allowed_types) )
		{
			log_message('error', 'Track share: Invalid entity type - ' . $entity_type);
			$this->output
					->set_status_header(400)
					->set_content_type('application/json', 'utf-8')
					->set_output(json_encode(['success' => FALSE, 'message' => lang('share_invalid_type')], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES))
					->_display();
			exit;
		}

		try {
			$this->VALID_UUIDv4($entity_id);
		} catch (Exception $e) {
			log_message('error', 'Track share: Invalid UUID - ' . $entity_id);
			$this->output
					->set_status_header(400)
					->set_content_type('application/json', 'utf-8')
					->set_output(json_encode(['success' => FALSE, 'message' => lang('share_invalid_id')], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES))
					->_display();
			exit;
		}

		// Check if entity exists based on type
		if( ! $this->_entity_exists($entity_type, $entity_id) )
		{
			log_message('error', 'Track share: Entity not found - ' . $entity_type . ': ' . $entity_id);
			$this->output
					->set_status_header(404)
					->set_content_type('application/json', 'utf-8')
					->set_output(json_encode(['success' => FALSE, 'message' => lang('share_entity_not_found')], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES))
					->_display();
			exit;
		}

		// Check if entity is already shared
		$is_shared = $this->share_model->is_entity_shared($entity_type, $entity_id);
		log_message('debug', 'Entity shared status: ' . ($is_shared ? 'true' : 'false'));

		if( ! $is_shared )
		{
			// Add new share record
			log_message('debug', 'Adding new share record');
			$share_id = $this->share_model->add_share($entity_type, $entity_id);

			if( $share_id )
			{
				log_message('debug', 'Share added successfully with ID: ' . $share_id);
				$this->output
						->set_status_header(200)
						->set_content_type('application/json', 'utf-8')
						->set_output(json_encode(['success' => TRUE, 'share_id' => $share_id], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES))
						->_display();
				exit;
			}
			else
			{
				log_message('error', 'Failed to add share record');
			}
		}
		else
		{
			// Entity already shared - track this share action
			log_message('debug', 'Updating existing share tracking');
			$updated = $this->share_model->update_share_date($entity_type, $entity_id);

			if( $updated )
			{
				// Get the existing share_id
				$share_id = $this->share_model->get_share_id($entity_type, $entity_id);
				
				log_message('debug', 'Share tracking updated successfully with ID: ' . $share_id);
				$this->output
						->set_status_header(200)
						->set_content_type('application/json', 'utf-8')
						->set_output(json_encode(['success' => TRUE, 'share_id' => $share_id, 'message' => lang('share_updated')], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES))
						->_display();
				exit;
			}
			else
			{
				log_message('error', 'Failed to update share tracking');
			}
		}

		// If we get here, something went wrong
		log_message('error', 'Track share: Unknown error occurred');
		$this->output
				->set_status_header(500)
				->set_content_type('application/json', 'utf-8')
				->set_output(json_encode(['success' => FALSE, 'message' => lang('share_error')], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES))
				->_display();
		exit;
	}

	/**
	 * Check if entity exists based on entity type
	 */
	private function _entity_exists($entity_type, $entity_id)
	{
		switch($entity_type) {
			case 'document':
				$this->load->model('document_model');
				return $this->document_model->get_document_exists($entity_id);
				
			case 'deviation':
				$this->load->model('deviation_model');
				return $this->deviation_model->exists($entity_id);
				
			case 'eventanalysis':
				// Check if eventanalysis model has exists method, otherwise implement check
				$this->load->model('eventanalysis_model');
				return method_exists($this->eventanalysis_model, 'exists') ? 
					$this->eventanalysis_model->exists($entity_id) : 
					$this->_check_generic_exists('eventanalysis_questions', 'uuid', $entity_id);
					
			case 'riskassessment':
				// Check if riskassessment model has exists method, otherwise implement check
				$this->load->model('risk_assessments_model');
				return method_exists($this->risk_assessments_model, 'exists') ? 
					$this->risk_assessments_model->exists($entity_id) :
					$this->_check_generic_exists('risk_assessments_department', 'uuid', $entity_id);
					
			default:
				return FALSE;
		}
	}

	/**
	 * Generic method to check if record exists in table
	 */
	private function _check_generic_exists($table, $uuid_field, $entity_id)
	{
		$table_name = $this->config->item($table) ?: $table;
		$query = $this->db->where($uuid_field, UUID_TO_BIN($entity_id))->get($table_name);
		return $query->num_rows() > 0;
	}
}
