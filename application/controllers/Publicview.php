<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Publicview extends MY_Controller
{
	public function __construct()
	{
		parent::__construct();
		$this->load->model('Share_model');
	}

	/**
	 * Public view method that accepts share_id
	 */
	public function view($share_id = NULL)
	{
		if (empty($share_id)) {
			show_404();
			return;
		}

		// Get share information
		$share = $this->Share_model->get_share_by_id($share_id);
		
		if (!$share) {
			show_404();
			return;
		}

		// Route to appropriate entity view based on entity_type
		switch ($share->entity_type) {
			case 'document':
				$this->_view_document($share->entity_id);
				break;
			case 'deviation':
				$this->_view_deviation($share->entity_id);
				break;
			case 'eventanalysis':
				$this->_view_eventanalysis($share->entity_id);
				break;
			case 'riskassessment':
				$this->_view_riskassessment($share->entity_id);
				break;
			default:
				show_404();
				break;
		}
	}

	/**
	 * View shared document
	 */
	private function _view_document($document_id)
	{
		$this->load->model('Documents_model');
		$this->load->model('Folders_model');
		$this->load->model('Menus_model');
		$this->load->model('Document_attachment_model');
		$this->load->model('Comments_model');
		$this->load->model('User_table_model');
		$this->load->model('Documents_type_model');
		$this->load->model('Documents_category_model');

		$document = $this->Documents_model->get_doc($document_id);
		
		if (!$document || $document->status !== 'published') {
			show_404();
			return;
		}

		$this->data['document'] = $document;
		$this->data['folders'] = $this->Folders_model->get_folder($document->folder_id);
		$this->data['menus'] = $this->Menus_model->get_menu($this->data['folders']->menu_id);
		$this->data['attachments'] = $this->Document_attachment_model->get_attachment_by_document($document_id);
		$this->data['comments'] = $this->Comments_model->get_comments($document_id, 'document');
		$this->data['authors'] = $this->User_table_model->get_users();
		$this->data['documents_type'] = $this->Documents_type_model->get_documents_type();
		$this->data['documents_category'] = $this->Documents_category_model->get_documents_category();

		// Set main attachment
		if (!empty($this->data['attachments'])) {
			foreach ($this->data['attachments'] as $attachment) {
				if ($attachment->main_file == 1) {
					$this->data['main_attachment'] = $attachment;
					break;
				}
			}
		}

		$this->load->view('general/documents/viewpublic', $this->data);
	}

	/**
	 * View shared deviation
	 */
	private function _view_deviation($deviation_id)
	{
		$this->load->model('Deviation_model');
		$this->load->model('User_table_model');

		$deviation = $this->Deviation_model->get_deviation($deviation_id);
		
		if (!$deviation) {
			show_404();
			return;
		}

		$this->data['deviation'] = $deviation;
		$this->data['user_data'] = $this->User_table_model->get_users();

		$this->load->view('general/deviation/viewpublic', $this->data);
	}

	/**
	 * View shared event analysis
	 */
	private function _view_eventanalysis($eventanalysis_id)
	{
		$this->load->model('Eventanalysis_model');
		$this->load->model('User_table_model');

		$eventanalysis = $this->Eventanalysis_model->get_eventanalysis($eventanalysis_id);
		
		if (!$eventanalysis) {
			show_404();
			return;
		}

		$this->data['eventanalysis'] = $eventanalysis;
		$this->data['user_data'] = $this->User_table_model->get_users();

		$this->load->view('general/eventanalysis/viewpublic', $this->data);
	}

	/**
	 * View shared risk assessment
	 */
	private function _view_riskassessment($riskassessment_id)
	{
		$this->load->model('Riskassessments_model');
		$this->load->model('User_table_model');

		$riskassessment = $this->Riskassessments_model->get_riskassessment($riskassessment_id);
		
		if (!$riskassessment) {
			show_404();
			return;
		}

		$this->data['riskassessment'] = $riskassessment;
		$this->data['user_data'] = $this->User_table_model->get_users();

		$this->load->view('general/riskassessments/viewpublic', $this->data);
	}
}
?>
