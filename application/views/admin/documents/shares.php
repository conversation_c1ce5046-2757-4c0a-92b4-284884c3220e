<?php $this->load->view('template/header'); ?>

  <!-- Content Wrapper. Contains page content -->
  <div class="content-wrapper">
    <!-- Content Header (Page header) -->
    <section class="content-header">
      <h1>
        <?php echo lang('shareable_documents'); ?>
        <small><?php echo lang('shareable_documents_desc'); ?></small>
      </h1>
    </section>

    <!-- Main content -->
		<section class="content">
		
		<!-- Statistics boxes -->
		<div class="row">
			<div class="col-lg-2 col-md-3 col-sm-6 col-xs-12">
				<div class="info-box">
					<span class="info-box-icon bg-green"><i class="fa fa-share-alt"></i></span>
					<div class="info-box-content">
						<span class="info-box-text">Total Shared</span>
						<span class="info-box-number"><?php echo $stats['total']; ?></span>
					</div>
				</div>
			</div>
			<div class="col-lg-2 col-md-3 col-sm-6 col-xs-12">
				<div class="info-box">
					<span class="info-box-icon bg-blue"><i class="fa fa-file-text"></i></span>
					<div class="info-box-content">
						<span class="info-box-text">Documents</span>
						<span class="info-box-number"><?php echo $stats['document']; ?></span>
					</div>
				</div>
			</div>
			<div class="col-lg-2 col-md-3 col-sm-6 col-xs-12">
				<div class="info-box">
					<span class="info-box-icon bg-yellow"><i class="fa fa-exclamation-triangle"></i></span>
					<div class="info-box-content">
						<span class="info-box-text">Deviations</span>
						<span class="info-box-number"><?php echo $stats['deviation']; ?></span>
					</div>
				</div>
			</div>
			<div class="col-lg-2 col-md-3 col-sm-6 col-xs-12">
				<div class="info-box">
					<span class="info-box-icon bg-red"><i class="fa fa-search"></i></span>
					<div class="info-box-content">
						<span class="info-box-text">Event Analysis</span>
						<span class="info-box-number"><?php echo $stats['eventanalysis']; ?></span>
					</div>
				</div>
			</div>
			<div class="col-lg-2 col-md-3 col-sm-6 col-xs-12">
				<div class="info-box">
					<span class="info-box-icon bg-purple"><i class="fa fa-shield"></i></span>
					<div class="info-box-content">
						<span class="info-box-text">Risk Assessments</span>
						<span class="info-box-number"><?php echo $stats['riskassessment']; ?></span>
					</div>
				</div>
			</div>
		</div>

		<!-- Entities list -->
		<div class="row">
			<div class="col-xs-12">
				<div class="box">
					<div class="box-header with-border">
						<h3 class="box-title">
							All Shared Entities
							<small>(<?php echo count($shared_entities); ?> entities)</small>
						</h3>
					</div>
					<div class="box-body">
						<?php if (!empty($shared_entities)): ?>
							<div class="table-responsive">
								<table class="table table-bordered table-striped">
									<thead>
										<tr>
											<th style="width: 40%;">Entity Name</th>
											<th style="width: 15%;">Type</th>
											<th style="width: 15%;">Shared By</th>
											<th style="width: 25%;">Share Date</th>
											<th style="width: 5%;">Actions</th>
										</tr>
									</thead>
									<tbody>
										<?php foreach ($shared_entities as $entity): ?>
											<tr>
												<td>
													<?php 
													// Create clickable entity name link
													$entity_url = '';
													switch($entity->entity_type) {
														case 'document':
															$entity_url = site_url('documents/view/' . $entity->entity_id);
															break;
														case 'deviation':
															$entity_url = site_url('deviations/view/' . $entity->entity_id);
															break;
														case 'eventanalysis':
															$entity_url = site_url('eventanalysis/view/' . $entity->entity_id);
															break;
														case 'riskassessment':
															$entity_url = site_url('riskassessments/view/' . $entity->entity_id);
															break;
													}
													?>
													<a href="<?php echo $entity_url; ?>" target="_blank" class="text-primary">
														<strong><?php echo htmlspecialchars($entity->entity_name ? $entity->entity_name : 'Unnamed Entity'); ?></strong>
													</a>
													<br>
													<?php 
													// Generate public share link using share_id
													$share_url = site_url('viewpublic/' . $entity->share_id);
													?>
													<small><a href="<?php echo $share_url; ?>" target="_blank" class="text-muted">View Public Share</a></small>
												</td>
												<td>
													<?php 
													$type_icon = '';
													$type_label = '';
													switch($entity->entity_type) {
														case 'document':
															$type_icon = '<i class="fa fa-file-text text-blue"></i>';
															$type_label = 'Document';
															break;
														case 'deviation':
															$type_icon = '<i class="fa fa-exclamation-triangle text-yellow"></i>';
															$type_label = 'Deviation';
															break;
														case 'eventanalysis':
															$type_icon = '<i class="fa fa-search text-red"></i>';
															$type_label = 'Event Analysis';
															break;
														case 'riskassessment':
															$type_icon = '<i class="fa fa-shield text-purple"></i>';
															$type_label = 'Risk Assessment';
															break;
														default:
															$type_icon = '<i class="fa fa-question text-gray"></i>';
															$type_label = ucfirst($entity->entity_type);
													}
													echo $type_icon . ' ' . $type_label;
													?>
												</td>
												<td><?php echo htmlspecialchars($entity->shared_by_name); ?></td>
												<td>
													<span title="<?php echo date('Y-m-d H:i:s', strtotime($entity->shared_date)); ?>">
														<?php echo date('Y-m-d H:i', strtotime($entity->shared_date)); ?>
													</span>
												</td>
												<td>
													<div class="btn-group">
														<button type="button" class="btn btn-sm btn-info" 
																onclick="copyShareLink('<?php echo site_url('viewpublic/' . $entity->share_id); ?>')"
																title="Copy Share Link">
															<i class="fa fa-copy"></i>
														</button>
														<button type="button" class="btn btn-sm btn-danger" 
																onclick="removeShare('<?php echo htmlspecialchars($entity->share_id); ?>')"
																title="Remove Share">
															<i class="fa fa-times"></i>
														</button>
													</div>
												</td>
											</tr>
										<?php endforeach; ?>
									</tbody>
								</table>
							</div>

						<?php else: ?>
							<div class="alert alert-info text-center">
								<i class="fa fa-info-circle"></i>
								No shared entities found. Entities will appear here when users share them.
							</div>
						<?php endif; ?>
					</div>
				</div>
			</div>
		</div>
		</section>
		<!-- /section -->
  </div>
  <!-- /.content-wrapper -->

<script>
function copyShareLink(url) {
	if (navigator.clipboard) {
		navigator.clipboard.writeText(url).then(function() {
			showNotification('<?php echo lang('shareable_link_copied'); ?>', 'success');
		}).catch(function() {
			fallbackCopyTextToClipboard(url);
		});
	} else {
		fallbackCopyTextToClipboard(url);
	}
}

function fallbackCopyTextToClipboard(text) {
	var textArea = document.createElement("textarea");
	textArea.value = text;
	document.body.appendChild(textArea);
	textArea.focus();
	textArea.select();
	try {
		var successful = document.execCommand('copy');
		if (successful) {
			showNotification('<?php echo lang('shareable_link_copied'); ?>', 'success');
		} else {
			showNotification('<?php echo lang('shareable_copy_failed'); ?>', 'error');
		}
	} catch (err) {
		showNotification('<?php echo lang('shareable_copy_failed'); ?>', 'error');
	}
	document.body.removeChild(textArea);
}

function removeShare(shareId) {
	if (!confirm('Are you sure you want to remove this share?')) {
		return;
	}

	var button = $('button[onclick="removeShare(\'' + shareId + '\')"]');

	$.ajax({
		url: '<?php echo site_url('admin/documents/remove_share'); ?>',
		type: 'POST',
		data: {
			share_id: shareId,
			'<?php echo $this->security->get_csrf_token_name(); ?>': '<?php echo $this->security->get_csrf_hash(); ?>'
		},
		dataType: 'json',
		beforeSend: function() {
			button.prop('disabled', true).html('<i class="fa fa-spinner fa-spin"></i>');
		},
		success: function(response) {
			if (response.success) {
				showNotification(response.message, 'success');
				// Remove the row from table
				button.closest('tr').fadeOut();
			} else {
				showNotification(response.message, 'error');
				button.prop('disabled', false).html('<i class="fa fa-times"></i>');
			}
		},
		error: function(xhr, status, error) {
			showNotification('<?php echo lang('shareable_remove_failed'); ?>', 'error');
			button.prop('disabled', false).html('<i class="fa fa-times"></i>');
		}
	});
}

function showNotification(message, type) {
	// Simple notification function - you can enhance this based on your notification system
	var alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
	var notification = $('<div class="alert ' + alertClass + ' alert-dismissible" role="alert">' +
		'<button type="button" class="close" data-dismiss="alert" aria-label="Close">' +
		'<span aria-hidden="true">&times;</span></button>' +
		message + '</div>');
	
	$('.content').prepend(notification);
	
	setTimeout(function() {
		notification.fadeOut();
	}, 5000);
}
</script>

<?php $this->load->view('template/footer'); ?>
