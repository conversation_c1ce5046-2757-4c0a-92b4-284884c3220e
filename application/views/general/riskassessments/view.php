<?php $this->load->view('template/header'); ?>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<!-- Content Header (Page header) -->
		<section class="content-header margin-bottom">
			<div class="float-right btn-group">
				<?php
				// var_dump($getRisk);
				echo icon_anchor('riskassessments/edit', $getRisk->ra_id, '<i class="fa fa-pencil" aria-hidden="true"></i>',
					array(
					'title' => 'Redigera riskanalys',
					'class' => 'btn btn-primary',
					)
				);
				?>
				<a class="btn btn-default" title="<?php echo lang('deviation_share') ?>" data-toggle="modal" data-target="#myModal" onclick="trackEntityShare('riskassessment', '<?php echo $ra_id; ?>')">
					<i class="fa fa-share-alt" aria-hidden="true" style="margin-right: 5px"></i>
				</a>
				<a href="javascript:goBack('/riskassessments');" title="<?php echo lang('back') ?>" class="btn btn-default"><i class="fa fa-reply" aria-hidden="true"></i></a>
			</div>
			<h1>Riskbedömning 
				<small><?php echo $getRisk->ra_id; ?></small>
			</h1>
		</section>
		<?php // @STEP2: html_escape ?>
		<!-- Main content -->
		<section class="content">
			<div class="no-print">
				<div class="callout callout-info" style="color: #172B4D !important;">
					Lagen kräver att man vid någon förändring av verksamheten (ny specialitet, ny teknisk
					utrustning, nya metoder och processer) skall utföra en riskbedömning där man listar de
					risker som kan uppstå, anger hur allvarlig varje risk är samt hur frekvent man bedömer att
					risken kan inträffa.
				</div>
			</div>
			<div class="row">
				<div class="col-md-3">
					<div class="box box-solid">
						<div class="box-body">
							<dl class="description-list">
								<?php
								foreach($fields as $key => $val):
									viewForm($val['id'],$val['input'],$val['required'],$val['title'],$val['description'],$val['value'],$val['values']);
								endforeach;
								?>
							</dl>
						</div>
					</div>
					<?php if( ! empty($getRiskAssessmentsDeviationMap) ): ?>
					<div class="box box-solid">
						<div class="box-header with-border">
							<h3 class="box-title"><?php echo count($getRiskAssessmentsDeviationMap) ==1 ? 'Sammankopplad avvikelse' : 'Sammankopplade avvikelser'; ?></h3>
						</div>
						<div class="box-body">
							<ul class="list-unstyled">
							<?php foreach($getRiskAssessmentsDeviationMap as $mapKey): ?>
								<li><?php echo safe_anchor('deviation/view', $mapKey, $getDeviationNames[$mapKey]); ?></li>
							<?php endforeach; ?>
							</ul>
						</div>
					</div>
					<?php endif; ?>
					<?php if( ! empty($getRiskAssessmentsEventAnalysisMap) ): ?>
					<div class="box box-solid">
						<div class="box-header with-border">
							<h3 class="box-title"><?php echo count($getRiskAssessmentsEventAnalysisMap) ==1 ? 'Sammankopplad händelseanalys' : 'Sammankopplade händelseanalyser'; ?></h3>
						</div>
						<div class="box-body">
							<ul class="list-unstyled">
							<?php foreach($getRiskAssessmentsEventAnalysisMap as $mapKey): ?>
								<li><?php echo safe_anchor('eventanalysis/view', $mapKey, $getEventAnalysisNames[$mapKey]); ?></li>
							<?php endforeach; ?>
							</ul>
						</div>
					</div>
					<?php endif; ?>
					<?php if( empty($getRiskAssessmentsDeviationMap) && empty($getRiskAssessmentsEventAnalysisMap) ): ?>
					<div class="box box-solid no-print">
						<div class="box-header with-border">
							<h3 class="box-title">Avvikelser och händelseanalyser</h3>
						</div>
						<div class="box-body">
							Inga sammankopplade funna.
						</div>
					</div>
					<?php endif; ?>
					<?php echo safe_anchor('riskassessments/connect', $getRisk->ra_id, 'Anslut avvikelser och händelseanalyser', array('class' => 'btn btn-primary btn-block margin-bottom no-print')); ?>
				</div>
				<div class="col-md-9">
					<?php foreach($getRisks as $risk): ?>
					<div class="box box-solid">
						<div class="box-body">
							<table class="table risksTabel no-data-table">
								<thead>
									<tr>
										<th>Risk</th>
										<th>Frekvens</th>
										<th>Allvarlighetsgrad</th>
										<th>Resultat av riskbedömning</th>
										<?php if( $this->auth_kiv ): ?>
										<th>Ansvarig</th>
										<?php endif; ?>
										<th>Skall vara klart</th>
										<th>Risken acceptabel</th>
										<th>Utfört</th>
									</tr>
								</thead>
								<tbody>
									<tr class="<?= $valueToText[$risk->occurrence+$risk->severity][1]; ?>">
										<td><?= $risk->risk; ?></td>
										<?php if($risk->occurrence) { ?>
										<td><?= preg_replace('/\(.*\)/','',$riskOccurrenceValues[$risk->occurrence]); ?></td>
										<?php } else { ?>
										<td></td>
										<?php }	?>
										<?php if($risk->severity) { ?>
										<td><?= preg_replace('/\(.*\)/','',$riskSeverityValues[$risk->severity]); ?></td>
										<?php } else { ?>
										<td></td>
										<?php }	?>
										<td><?= $valueToText[$risk->occurrence+$risk->severity][0]; ?></td>
										<?php if( $this->auth_kiv ): ?>
										<td><?= username($risk->responsible); ?></td>
										<?php endif; ?>
										<td><?= $risk->done; ?></td>
										<td><?= $risk->acceptable?'Ja':'Nej'; ?></td>
										<td><?= $risk->finished; ?></td>
									</tr>
									<tr>
										<td colspan="8">
											<p><?= nl2br($risk->explanation); ?></p>
											<b>Åtgärd:</b> <?= $risk->measure; ?>
										</td>
									</tr>
								</tbody>
							</table>
						</div>
					</div>
					<?php endforeach; ?>
				</div>
				<!-- /.col-md-9-->
			</div>
			<!-- /.row -->
		</section>
		<!-- /section -->
  </div>
  <!-- /.content-wrapper -->
	<div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
		<div class="modal-dialog">
			<div class="modal-content">
				<div class="modal-header">
					<button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
					<h4 class="modal-title" id="myModalLabel"><?php echo lang('deviation_share_link'); ?></h4>
				</div>
				<div class="modal-body">
					<a id="copy-text" href="https://<?php echo $_SERVER['SERVER_NAME'] ?>/viewriskassessments/<?php echo $ra_id; ?>" target="_blank">https://<?php echo $_SERVER['SERVER_NAME'] ?>/viewriskassessments/<?php echo $ra_id; ?></a>
					<button onClick="navigator.clipboard.writeText(document.getElementById('copy-text').innerText)"
					class="btn btn-default" style="float: right">
						<i class="fa fa-copy" aria-hidden="true"></i>
					</button>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
				</div>
			</div>
		</div>
	</div>

	<script>
	function trackEntityShare(entityType, entityId) {
		$.ajax({
			url: '<?php echo site_url('share/track'); ?>',
			type: 'POST',
			data: {
				entity_type: entityType,
				entity_id: entityId,
				'<?php echo $this->security->get_csrf_token_name(); ?>': '<?php echo $this->security->get_csrf_hash(); ?>'
			},
			dataType: 'json',
			success: function(response) {
				console.log('Share tracked successfully:', response);
				if (response.success && response.share_id) {
					// Update the share URL to use share_id instead of entity_id
					var shareUrl = 'https://<?php echo $_SERVER['SERVER_NAME']; ?>/viewpublic/' + response.share_id;
					$('#copy-text').attr('href', shareUrl).text(shareUrl);
					console.log('Share URL updated:', shareUrl);
				}
			},
			error: function(xhr, status, error) {
				console.log('Failed to track share:', error);
				console.log('Response:', xhr.responseText);
			}
		});
	}
	</script>
<?php $this->load->view('template/footer');