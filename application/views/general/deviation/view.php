<?php $this->load->view('template/header'); ?>
<?php 
  $issueTitle = '';
  $issueType = '';
  $issueDescription = '';
  $cur_fields = isset($deviation_page1_fields) ? $deviation_page1_fields : $deviation['fields'];
  $cur_options = isset($options_page1) ? $options_page1 : $options;
  foreach($cur_fields as $key => $val) {
    if ($val->input == 'input' && $val->required_kvalprak == 1) {
      $issueTitle = $val->answer;
    }
    if ($val->input == 'dropdown' && $val->required_kvalprak == 1) {
      $o = isset($cur_options[$val->df_id]) ? $cur_options[$val->df_id] : array();
      $issueType = $o[$val->answer]->name;
    }
    if ($val->input == 'text_wysiwyg' && $val->required_kvalprak == 1 && isset($val->answer)) {
      $issueDescription = trim(strip_tags($val->answer));
    }
  }
  if (isset($deviation_page2_fields)) {
    foreach($deviation_page2_fields as $key => $val) {
      if ($val->input == 'input' && $val->required_kvalprak == 1) {
        $issueTitle = $val->answer;
      }
      if ($val->input == 'dropdown' && $val->required_kvalprak == 1) {
        $o = isset($options_page1[$val->df_id]) ? $options_page1[$val->df_id] : array();
        $issueType = $o[$val->answer]->name;
      }
      if ($val->input == 'text_wysiwyg' && $val->required_kvalprak == 1) {
        $issueDescription = trim(strip_tags($val->answer));
      }
    }
  }
  $issueTitle = str_replace('"', '\'', $issueTitle);
  $issueType = str_replace('"', '\'', $issueType);
  $issueDescription = str_replace('"', '\'', $issueDescription);
  $issueDescription = str_replace('&nbsp;', ' ', $issueDescription);
  $issueDescription = str_replace('\r\n', ' ', $issueDescription);
  $issueDescription = str_replace('\n', ' ', $issueDescription);
  $issueDescription = str_replace('\r', ' ', $issueDescription);
?>
<script type="text/javascript">
	function openAssistant(){
		const frame = document.getElementById("optobot")
    frame.setAttribute('src', `https://orna-analys.kvalprak.se/chatbot/<?php echo $this->config->item('program_name') ?>?solve=true&open=true&issueTitle=<?php echo $issueTitle ?>&issueType=<?php echo $issueType ?>&issueDescription=<?php echo $issueDescription ?>`);
	}
</script>
	<!-- Content Wrapper. Contains page content -->
	<div class="content-wrapper">
		<!-- Content Header (Page header) -->
		<section class="content-header margin-bottom">
			<div class="btn-group float-right">
				<?php if (in_array($this->auth_email, ['<EMAIL>', '<EMAIL>'])): ?>
				<a title="Redigera" href="#" onClick="openAssistant()" class="btn btn-primary mr-2">Föreslå lösning på avvikelsen</a>
				<?php endif; ?>
				<?php
				if( ! empty($rights['update']) ):
					echo icon_anchor('deviation/edit/1', $a_id ,lang('edit'),
						array(
						'title' => 'Redigera',
						'class' => 'btn btn-primary',
						)
					);
					echo icon_anchor('deviation/attachments', $a_id ,'<i class="fa fa-paperclip" aria-hidden="true"></i>',
						array(
						'title' => 'Bilagor',
						'class' => 'btn btn-default'
						)
					);
				endif;
				?>
				<a class="btn btn-default" title="<?php echo lang('deviation_share') ?>" data-toggle="modal" data-target="#myModal" onclick="trackEntityShare('deviation', '<?php echo $a_id; ?>')">
					<i class="fa fa-share-alt" aria-hidden="true" style="margin-right: 5px"></i>
				</a>
				<a href="javascript:goBack('/deviation');" title="<?php echo lang('back') ?>" class="btn btn-default"><i class="fa fa-reply" aria-hidden="true"></i></a>
			</div>
			<h1>Avvikelserapport
				<small><?php 
				$heading = '';
				foreach($deviation['fields'] as $key => $val) {
					if ($val->input == 'input' && $val->required_kvalprak == 1) {
						$heading = $val->answer;
						break;
					}
				}
				echo $heading; ?></small>
			</h1>
		</section>
		<?php // @STEP2: html_escape ?>
		<!-- Main content -->
		<section class="content">
			<div class="row max-width-1225">
				<h1 class="show-in-print"> <?php echo $heading; ?></h1>
				<div class="col-md-8">
					<div class="box box-solid">
						<div class="box-body no-padding">
							<div class="accordion-header"> 
							<h4>
								Steg 1: <?php echo lang('stage1_reported');?>
								<a data-toggle="collapse" data-target="#stage1" class="float-right">
									<i class="fa fa-chevron-down" aria-hidden="true"></i>
									<i class="fa fa-chevron-up" aria-hidden="true"></i>
								</a>
							</h4>
							<div class="box-body no-padding collapse in show" id="stage1" aria-hidden="true">
								<?php 
									foreach($deviation['fields'] as $key => $val):
										if ($val->page == 1) {
											$s = isset($selected[$val->df_id]) ? $selected[$val->df_id] : $val->answer;
											if( ! empty($s) )
											{
												$o = isset($options[$val->df_id]) ? $options[$val->df_id] : array();
												forms_view($val->input, $val->df_id, $val->title, '', $o, $s, NULL, '');
											}
										}
									endforeach;
								?>
							</div>
							</div>

							<div class="accordion-header"> 
							<h4>
								Steg 2: <?php echo lang('stage2_started');?>
								<a data-toggle="collapse" data-target="#stage2" class="float-right">
									<i class="fa fa-chevron-down" aria-hidden="true"></i>
									<i class="fa fa-chevron-up" aria-hidden="true"></i>
								</a>
							</h4>
							<div class="box-body no-padding collapse in show" id="stage2" aria-hidden="true">
								<?php 
									foreach($deviation['fields'] as $key => $val):
										if ($val->page == 2 && $rightsPage2) {
											$s = isset($selected[$val->df_id]) ? $selected[$val->df_id] : $val->answer;
											if( ! empty($s) )
											{
												$o = isset($options[$val->df_id]) ? $options[$val->df_id] : array();
												forms_view($val->input, $val->df_id, $val->title, '', $o, $s, NULL, '');
											}
										}
									endforeach;
								?>
							</div>
							</div>

							<div class="accordion-header"> 
							<h4>
								Steg 3: <?php echo lang('stage3_decision');?>
								<a data-toggle="collapse" data-target="#stage3" class="float-right">
									<i class="fa fa-chevron-down" aria-hidden="true"></i>
									<i class="fa fa-chevron-up" aria-hidden="true"></i>
								</a>
							</h4>
							<div class="box-body no-padding collapse in show" id="stage3" aria-hidden="true">
								<?php 
									foreach($deviation['fields'] as $key => $val):
										if ($val->page == 3 && $rightsPage3) {
											$s = isset($selected[$val->df_id]) ? $selected[$val->df_id] : $val->answer;
											if( ! empty($s) )
											{
												$o = isset($options[$val->df_id]) ? $options[$val->df_id] : array();
												forms_view($val->input, $val->df_id, $val->title, '', $o, $s, NULL, '');
											}
										}
									endforeach;
								?>
							</div>
							</div>
						</div>
						<!-- /.box-body -->
					</div>
					<!-- /. box -->
				</div>
				<!-- /.col -->
				<div class="col-md-4">
					<div class="right-panel">
					<?php if( ! empty($getDeviationEventAnalysisMap) ): ?>
					<div class="box box-solid">
						<div class="box-header with-border">
							<h3 class="box-title"><?php echo count($getDeviationEventAnalysisMap) ==1 ? 'Sammankopplad händelseanalys' : 'Sammankopplade händelseanalyser'; ?></h3>
						</div>
						<div class="box-body">
							<ul class="list-unstyled">
							<?php foreach($getDeviationEventAnalysisMap as $mapKey): ?>
								<li><?php echo safe_anchor('eventanalysis/view', $mapKey, $getEventAnalysisNames[$mapKey]); ?></li>
							<?php endforeach; ?>
							</ul>
						</div>
					</div>
					<?php endif; ?>
					<?php if( ! empty($getDeviationRiskAssesmentsMap) ): ?>
					<div class="box box-solid">
						<div class="box-header with-border">
							<h3 class="box-title"><?php echo count($getDeviationRiskAssesmentsMap) ==1 ? 'Sammankopplad riskbedömning' : 'Sammankopplade riskbedömningar'; ?></h3>
						</div>
						<div class="box-body">
							<ul class="list-unstyled">
							<?php foreach($getDeviationRiskAssesmentsMap as $mapKey): ?>
								<li><?php echo safe_anchor('riskassessments/view', $mapKey, $getRiskAssessmentsNames[$mapKey]); ?></li>
							<?php endforeach; ?>
							</ul>
						</div>
					</div>
					<?php endif; ?>
					<?php if( empty($getDeviationEventAnalysisMap) && empty($getDeviationRiskAssesmentsMap) ): ?>
					<div class="box box-solid no-print">
						<div class="box-header with-border">
							<h3 class="box-title">Händelseanalyser och riskbedömningar</h3>
						</div>
						<div class="box-body">
							Inga sammankopplade funna.
						</div>
					</div>
					<?php endif; ?>
					<?php echo safe_anchor('deviation/connect', $a_id, 'Anslut händelseanalyser och riskbedömningar', array('class' => 'btn btn-primary btn-block margin-bottom no-print')); ?>
					<?php if( !empty($attachments) ): ?>
					<div class="box box-solid">
						<div class="box-header with-border">
							<h3 class="box-title"><?php echo lang('documents_attachments'); ?></h3>
						</div>
						<div class="box-body">
							<ul class="list-unstyled">
							<?php
							foreach($attachments as $attachment):
								echo '<li>' . safe_anchor('deviation/download', $attachment->attachment_id, $attachment->file_name, ['target' => '_blank']) . '</li>';
							endforeach;
							?>
							</ul>
						</div>
					</div>
					<?php endif; ?>
					</div>
				</div>
				<!-- /.col -->
			</div>
			<!-- /.row -->
		</section>
		<!-- /.content -->
	</div>
	<!-- /.content-wrapper -->
	<div class="modal fade" id="myModal" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
		<div class="modal-dialog">
			<div class="modal-content">
				<div class="modal-header">
					<h4 class="modal-title" id="myModalLabel"><?php echo lang('deviation_share_link'); ?></h4>
					<button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
				</div>
				<div class="modal-body">
					<a id="copy-text" href="https://<?php echo $_SERVER['SERVER_NAME'] ?>/viewdeviation/<?php echo $a_id; ?>" target="_blank">https://<?php echo $_SERVER['SERVER_NAME'] ?>/viewdeviation/<?php echo $a_id; ?></a>
					<button onClick="navigator.clipboard.writeText(document.getElementById('copy-text').innerText)"
					class="btn btn-default" style="float: right">
						<i class="fa fa-copy" aria-hidden="true"></i>
					</button>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-default" data-dismiss="modal">Close</button>
				</div>
			</div>
		</div>
	</div>

	<script>
	function trackEntityShare(entityType, entityId) {
		$.ajax({
			url: '<?php echo site_url('share/track'); ?>',
			type: 'POST',
			data: {
				entity_type: entityType,
				entity_id: entityId,
				'<?php echo $this->security->get_csrf_token_name(); ?>': '<?php echo $this->security->get_csrf_hash(); ?>'
			},
			dataType: 'json',
			success: function(response) {
				console.log('Share tracked successfully:', response);
				if (response.success && response.share_id) {
					// Update the share URL to use share_id instead of entity_id
					var shareUrl = 'https://<?php echo $_SERVER['SERVER_NAME']; ?>/viewpublic/' + response.share_id;
					$('#copy-text').attr('href', shareUrl).text(shareUrl);
					console.log('Share URL updated:', shareUrl);
				}
			},
			error: function(xhr, status, error) {
				console.log('Failed to track share:', error);
				console.log('Response:', xhr.responseText);
			}
		});
	}
	</script>
<?php $this->load->view('template/footer'); ?>