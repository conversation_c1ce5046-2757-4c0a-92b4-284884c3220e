<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Share_model extends Auth_Model
{
	public function __construct()
	{
		parent::__construct();
	}

	/**
	 * Add a new shared entity
	 */
	public function add_share($entity_type, $entity_id)
	{
		$share_id = UUIDv4();
		$data = array(
			'share_id'    => UUID_TO_BIN($share_id),
			'entity_type' => $entity_type,
			'entity_id'   => UUID_TO_BIN($entity_id),
			'shared_by'   => UUID_TO_BIN($this->auth_user_id),
			'shared_date' => date('Y-m-d H:i:s')
		);

		if ($this->db->insert($this->db_table('shares'), $data)) {
			return $share_id;
		}
		return FALSE;
	}

	/**
	 * Remove entity share by entity details
	 */
	public function remove_entity_share($entity_type, $entity_id)
	{
		$this->db->where('entity_type', $entity_type);
		$this->db->where('entity_id', UUID_TO_BIN($entity_id));
		return $this->db->delete($this->db_table('shares'));
	}

	/**
	 * Check if entity is shared
	 */
	public function is_entity_shared($entity_type, $entity_id)
	{
		$this->db->where('entity_type', $entity_type);
		$this->db->where('entity_id', UUID_TO_BIN($entity_id));
		$query = $this->db->get($this->db_table('shares'));
		return $query->num_rows() > 0;
	}

	/**
	 * Update share date
	 */
	public function update_share_date($entity_type, $entity_id)
	{
		$this->db->where('entity_type', $entity_type);
		$this->db->where('entity_id', UUID_TO_BIN($entity_id));
		$data = array('shared_date' => date('Y-m-d H:i:s'));
		return $this->db->update($this->db_table('shares'), $data);
	}

	/**
	 * Get all shared documents for backward compatibility
	 */
	public function get_all_shared_documents($limit = NULL, $offset = NULL, $search = NULL)
	{
		$this->db->select('shares.share_id, shares.entity_id, shares.shared_by, shares.shared_date,
			documents.name as document_name, documents.status, documents.folder_id,
			folders.name as folder_name, menus.name as menu_name,
			users.name as shared_by_name');

		$this->db->from($this->db_table('shares'));
		$this->db->join($this->db_table('documents'), 'documents.document_id = shares.entity_id', 'left');
		$this->db->join($this->db_table('folders'), 'folders.folder_id = documents.folder_id', 'left');
		$this->db->join($this->db_table('menus'), 'menus.menu_id = folders.menu_id', 'left');
		$this->db->join($this->db_table('user_table') . ' AS users', 'users.user_id = shares.shared_by', 'left');
		
		$this->db->where('shares.entity_type', 'document');
		$this->db->where('documents.status', 'published');

		// Search functionality
		if (!empty($search)) {
			$this->db->group_start();
			$this->db->like('documents.name', $search);
			$this->db->or_like('documents.description', $search);
			$this->db->or_like('folders.name', $search);
			$this->db->or_like('menus.name', $search);
			$this->db->group_end();
		}

		// Order by shared_date in descending order
		$this->db->order_by('shares.shared_date', 'DESC');

		if ($limit !== NULL) {
			$this->db->limit($limit, $offset);
		}

		$query = $this->db->get();
		
		if ($query->num_rows() > 0) {
			$results = $query->result();
			foreach ($results as $result) {
				$result->share_id = BIN_TO_UUID($result->share_id);
				$result->entity_id = BIN_TO_UUID($result->entity_id);
				$result->shared_by = BIN_TO_UUID($result->shared_by);
			}
			return $results;
		}
		
		return array();
	}

	/**
	 * Get count of shared documents
	 */
	public function get_shared_documents_count($search = NULL)
	{
		$this->db->from($this->db_table('shares'));
		$this->db->join($this->db_table('documents'), 'documents.document_id = shares.entity_id', 'left');
		$this->db->join($this->db_table('folders'), 'folders.folder_id = documents.folder_id', 'left');
		$this->db->join($this->db_table('menus'), 'menus.menu_id = folders.menu_id', 'left');
		
		$this->db->where('shares.entity_type', 'document');
		$this->db->where('documents.status', 'published');

		// Search functionality
		if (!empty($search)) {
			$this->db->group_start();
			$this->db->like('documents.name', $search);
			$this->db->or_like('documents.description', $search);
			$this->db->or_like('folders.name', $search);
			$this->db->or_like('menus.name', $search);
			$this->db->group_end();
		}

		return $this->db->count_all_results();
	}

	/**
	 * Get sharing statistics
	 */
	public function get_sharing_stats()
	{
		// Total shared entities (all types)
		$total_shared = $this->db->count_all_results($this->db_table('shares'));

		// Count by entity type
		$this->db->where('entity_type', 'document');
		$document_count = $this->db->count_all_results($this->db_table('shares'));

		$this->db->where('entity_type', 'deviation');
		$deviation_count = $this->db->count_all_results($this->db_table('shares'));

		$this->db->where('entity_type', 'eventanalysis');
		$eventanalysis_count = $this->db->count_all_results($this->db_table('shares'));

		$this->db->where('entity_type', 'riskassessment');
		$riskassessment_count = $this->db->count_all_results($this->db_table('shares'));

		// Entities shared this week (all types)
		$this->db->where('shared_date >=', date('Y-m-d 00:00:00', strtotime('monday this week')));
		$this->db->where('shared_date <=', date('Y-m-d 23:59:59', strtotime('sunday this week')));
		$this_week = $this->db->count_all_results($this->db_table('shares'));

		return array(
			'total' => $total_shared,
			'document' => $document_count,
			'deviation' => $deviation_count,
			'eventanalysis' => $eventanalysis_count,
			'riskassessment' => $riskassessment_count,
			'this_week' => $this_week,
			// Keep backward compatibility
			'total_shared' => $total_shared
		);
	}

	/**
	 * Get all shared entities for admin view with entity names
	 */
	public function get_all_shared_entities($limit = NULL, $offset = NULL, $search = NULL)
	{
		$this->db->select('shares.share_id, shares.entity_type, shares.entity_id, shares.shared_by,
			shares.shared_date, users.name as shared_by_name,
			documents.name as document_name,
			deviation.name as deviation_name,
			eventanalysis.name as eventanalysis_name,
			risk_assessments.name as riskassessment_name');

		$this->db->from($this->db_table('shares'));
		$this->db->join($this->db_table('user_table') . ' AS users', 'users.user_id = shares.shared_by', 'left');
		
		// Join with entity tables to get names
		$this->db->join($this->db_table('documents'), 'documents.document_id = shares.entity_id AND shares.entity_type = "document"', 'left');
		$this->db->join($this->db_table('deviation'), 'deviation.deviation_id = shares.entity_id AND shares.entity_type = "deviation"', 'left');
		$this->db->join($this->db_table('eventanalysis'), 'eventanalysis.eventanalysis_id = shares.entity_id AND shares.entity_type = "eventanalysis"', 'left');
		$this->db->join($this->db_table('risk_assessments'), 'risk_assessments.ra_id = shares.entity_id AND shares.entity_type = "riskassessment"', 'left');

		// Search functionality
		if (!empty($search)) {
			$this->db->group_start();
			$this->db->like('shares.entity_type', $search);
			$this->db->or_like('documents.name', $search);
			$this->db->or_like('deviation.name', $search);
			$this->db->or_like('eventanalysis.name', $search);
			$this->db->or_like('risk_assessments.name', $search);
			$this->db->group_end();
		}

		// Order by shared_date in descending order
		$this->db->order_by('shares.shared_date', 'DESC');

		if ($limit !== NULL) {
			$this->db->limit($limit, $offset);
		}

		$query = $this->db->get();
		
		if ($query->num_rows() > 0) {
			$results = $query->result();
			foreach ($results as $result) {
				$result->share_id = BIN_TO_UUID($result->share_id);
				$result->entity_id = BIN_TO_UUID($result->entity_id);
				$result->shared_by = BIN_TO_UUID($result->shared_by);
				
				// Set entity name based on type
				switch($result->entity_type) {
					case 'document':
						$result->entity_name = $result->document_name;
						break;
					case 'deviation':
						$result->entity_name = $result->deviation_name;
						break;
					case 'eventanalysis':
						$result->entity_name = $result->eventanalysis_name;
						break;
					case 'riskassessment':
						$result->entity_name = $result->riskassessment_name;
						break;
					default:
						$result->entity_name = 'Unknown Entity';
				}
			}
			return $results;
		}
		
		return array();
	}

	/**
	 * Get share by share_id
	 */
	public function get_share_by_id($share_id)
	{
		$this->db->where('share_id', UUID_TO_BIN($share_id));
		$query = $this->db->get($this->db_table('shares'));
		
		if ($query->num_rows() > 0) {
			$result = $query->row();
			$result->share_id = BIN_TO_UUID($result->share_id);
			$result->entity_id = BIN_TO_UUID($result->entity_id);
			$result->shared_by = BIN_TO_UUID($result->shared_by);
			return $result;
		}
		
		return FALSE;
	}

	/**
	 * Get share_id by entity details
	 */
	public function get_share_id($entity_type, $entity_id)
	{
		$this->db->select('share_id');
		$this->db->from($this->db_table('shares'));
		$this->db->where('entity_type', $entity_type);
		$this->db->where('entity_id', UUID_TO_BIN($entity_id));
		
		$query = $this->db->get();
		
		if ($query->num_rows() > 0) {
			$result = $query->row();
			return BIN_TO_UUID($result->share_id);
		}
		
		return FALSE;
	}

	/**
	 * Remove a share by share_id
	 */
	public function remove_share_by_id($share_id)
	{
		$this->db->where('share_id', UUID_TO_BIN($share_id));
		return $this->db->delete($this->db_table('shares'));
	}
}
?>
