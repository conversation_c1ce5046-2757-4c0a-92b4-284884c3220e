<?php
defined('BASEPATH') OR exit('No direct script access allowed');

/**
 * Backward compatibility wrapper for Document sharing
 * This model now uses the generic shares table but maintains the same interface
 */
class Document_share_model extends Auth_Model
{
	public function __construct()
	{
		parent::__construct();
		// Load the new Share_model for actual operations
		$this->load->model('Share_model');
	}

	/**
	 * Add a new shared document (backward compatibility)
	 */
	public function add_share($document_id, $share_url = '')
	{
		// Note: share_url is now ignored as we use share_id based URLs
		return $this->Share_model->add_share('document', $document_id);
	}

	/**
	 * Get all shared documents
	 */
	public function get_all_shared_documents($limit = NULL, $offset = NULL, $search = NULL)
	{
		return $this->Share_model->get_all_shared_documents($limit, $offset, $search);
	}

	/**
	 * Get count of shared documents
	 */
	public function get_shared_documents_count($search = NULL)
	{
		return $this->Share_model->get_shared_documents_count($search);
	}

	/**
	 * Check if document is shared
	 */
	public function is_document_shared($document_id)
	{
		return $this->Share_model->is_entity_shared('document', $document_id);
	}

	/**
	 * Update share date (backward compatibility)
	 */
	public function update_share_date($document_id)
	{
		return $this->Share_model->update_share_date('document', $document_id);
	}

	/**
	 * Get shared documents for admin view
	 */
	public function get_shared_documents($limit = NULL, $offset = NULL, $search = NULL)
	{
		return $this->Share_model->get_all_shared_documents($limit, $offset, $search);
	}

	/**
	 * Get share by document_id
	 */
	public function get_share_by_document($document_id)
	{
		$share_id = $this->Share_model->get_share_id('document', $document_id);
		if ($share_id) {
			return $this->Share_model->get_share_by_id($share_id);
		}
		return FALSE;
	}

	/**
	 * Remove document share
	 */
	public function remove_document_share($document_id)
	{
		return $this->Share_model->remove_entity_share('document', $document_id);
	}

	/**
	 * Activate document share (deprecated - no longer needed)
	 */
	public function activate_document_share($document_id)
	{
		// This method is deprecated as we no longer use is_active
		return TRUE;
	}

	/**
	 * Deactivate document share (deprecated - now removes share completely)
	 */
	public function deactivate_document_share($document_id)
	{
		return $this->remove_document_share($document_id);
	}
}
?>
