<?php
defined('BASEPATH') OR exit('No direct script access allowed');

class Share_model extends Auth_Model
{
	public function __construct()
	{
		parent::__construct();
	}

	/**
	 * Add a new shared entity
	 */
	public function add_share($entity_type, $entity_id)
	{
		$share_id = UUIDv4();
		$data = array(
			'share_id'    => UUID_TO_BIN($share_id),
			'entity_type' => $entity_type,
			'entity_id'   => UUID_TO_BIN($entity_id),
			'shared_by'   => UUID_TO_BIN($this->auth_user_id),
			'shared_date' => date('Y-m-d H:i:s')
		);

		log_message('debug', 'Attempting to insert share data for ' . $entity_type . ': ' . $entity_id);
		log_message('debug', 'Table name: ' . $this->db_table('shares'));

		if ($this->db->insert($this->db_table('shares'), $data)) {
			log_message('debug', 'Share inserted successfully with ID: ' . $share_id);
			return $share_id;
		}

		log_message('error', 'Failed to insert share: ' . $this->db->last_query());
		log_message('error', 'Database error: ' . json_encode($this->db->error()));
		return FALSE;
	}

	/**
	 * Get all shared documents
	 */
	public function get_all_shared_documents($limit = NULL, $offset = NULL, $search = NULL)
	{
		$this->db->select('shares.share_id, shares.entity_id as document_id, shares.shared_by,
			shares.shared_date,
			documents.name as document_name, documents.description as document_description,
			documents.document_type, documents.last_revised, documents.valid_until,
			folders.name as folder_name, menus.name as menu_name, users.name as shared_by_name,
			document_attachment.file_ext');

		$this->db->from($this->db_table('shares'));
		$this->db->join($this->db_table('documents'), 'documents.document_id = shares.entity_id', 'left');
		$this->db->join($this->db_table('folders'), 'folders.folder_id = documents.folder_id', 'left');
		$this->db->join($this->db_table('menus'), 'menus.menu_id = folders.menu_id', 'left');
		$this->db->join($this->db_table('user_table') . ' AS users', 'users.user_id = shares.shared_by', 'left');
		$this->db->join($this->db_table('document_attachment'), 'document_attachment.document_id = documents.document_id', 'left');
		
		$this->db->where('shares.entity_type', 'document');
		$this->db->where('documents.status', 'published');

		// Search functionality
		if (!empty($search)) {
			$this->db->group_start();
			$this->db->like('documents.name', $search);
			$this->db->or_like('documents.description', $search);
			$this->db->or_like('folders.name', $search);
			$this->db->or_like('menus.name', $search);
			$this->db->group_end();
		}

		// Order by shared_date in descending order
		$this->db->order_by('shares.shared_date', 'DESC');

		if ($limit !== NULL) {
			$this->db->limit($limit, $offset);
		}

		$query = $this->db->get();
		
		if ($query->num_rows() > 0) {
			$results = $query->result();
			foreach ($results as $result) {
				$result->share_id = BIN_TO_UUID($result->share_id);
				$result->document_id = BIN_TO_UUID($result->document_id);
				$result->shared_by = BIN_TO_UUID($result->shared_by);
			}
			return $results;
		}
		
		return array();
	}

	/**
	 * Get count of shared documents
	 */
	public function get_shared_documents_count($search = NULL)
	{
		$this->db->from($this->db_table('shares'));
		$this->db->join($this->db_table('documents'), 'documents.document_id = shares.entity_id', 'left');
		$this->db->join($this->db_table('folders'), 'folders.folder_id = documents.folder_id', 'left');
		$this->db->join($this->db_table('menus'), 'menus.menu_id = folders.menu_id', 'left');
		
		$this->db->where('shares.entity_type', 'document');
		$this->db->where('documents.status', 'published');

		// Search functionality
		if (!empty($search)) {
			$this->db->group_start();
			$this->db->like('documents.name', $search);
			$this->db->or_like('documents.description', $search);
			$this->db->or_like('folders.name', $search);
			$this->db->or_like('menus.name', $search);
			$this->db->group_end();
		}

		return $this->db->count_all_results();
	}

	/**
	 * Check if an entity is shared
	 */
	public function is_entity_shared($entity_type, $entity_id)
	{
		$this->db->from($this->db_table('shares'));
		$this->db->where('entity_type', $entity_type);
		$this->db->where('entity_id', UUID_TO_BIN($entity_id));
		
		$query = $this->db->get();
		return ($query->num_rows() > 0);
	}

	/**
	 * Check if document is already shared (compatibility method)
	 */
	public function is_document_shared($document_id)
	{
		return $this->is_entity_shared('document', $document_id);
	}

	/**
	 * Get share information for an entity
	 */
	public function get_entity_share($entity_type, $entity_id)
	{
		$this->db->where('entity_type', $entity_type);
		$this->db->where('entity_id', UUID_TO_BIN($entity_id));
		$query = $this->db->get($this->db_table('shares'));

		if ($query->num_rows() > 0) {
			$result = $query->row();
			$result->share_id = BIN_TO_UUID($result->share_id);
			$result->entity_id = BIN_TO_UUID($result->entity_id);
			$result->shared_by = BIN_TO_UUID($result->shared_by);
			return $result;
		}

		return NULL;
	}

	/**
	 * Get share information for a document (compatibility method)
	 */
	public function get_document_share($document_id)
	{
		return $this->get_entity_share('document', $document_id);
	}

	/**
	 * Update share tracking when entity is shared again
	 */
	public function update_share_date($entity_type, $entity_id)
	{
		$data = array(
			'shared_date' => date('Y-m-d H:i:s')
		);

		$this->db->where('entity_type', $entity_type);
		$this->db->where('entity_id', UUID_TO_BIN($entity_id));

		return $this->db->update($this->db_table('shares'), $data);
	}

	/**
	 * Update share tracking for document (compatibility method)
	 */
	public function update_document_share_date($document_id)
	{
		return $this->update_share_date('document', $document_id);
	}

	/**
	 * Delete share permanently
	 */
	public function delete_share($entity_type, $entity_id)
	{
		$this->db->where('entity_type', $entity_type);
		$this->db->where('entity_id', UUID_TO_BIN($entity_id));
		return $this->db->delete($this->db_table('shares'));
	}

	/**
	 * Remove share by document_id (compatibility method)
	 */
	public function remove_share($document_id)
	{
		return $this->delete_share('document', $document_id);
	}

	/**
	 * Get sharing statistics
	 */
	public function get_sharing_stats()
	{
		// Total shared entities (all types)
		$total_shared = $this->db->count_all_results($this->db_table('shares'));

		// Count by entity type
		$this->db->where('entity_type', 'document');
		$document_count = $this->db->count_all_results($this->db_table('shares'));

		$this->db->where('entity_type', 'deviation');
		$deviation_count = $this->db->count_all_results($this->db_table('shares'));

		$this->db->where('entity_type', 'eventanalysis');
		$eventanalysis_count = $this->db->count_all_results($this->db_table('shares'));

		$this->db->where('entity_type', 'riskassessment');
		$riskassessment_count = $this->db->count_all_results($this->db_table('shares'));

		// Entities shared this week (all types)
		$this->db->where('shared_date >=', date('Y-m-d 00:00:00', strtotime('monday this week')));
		$this->db->where('shared_date <=', date('Y-m-d 23:59:59', strtotime('sunday this week')));
		$this_week = $this->db->count_all_results($this->db_table('shares'));

		return array(
			'total' => $total_shared,
			'document' => $document_count,
			'deviation' => $deviation_count,
			'eventanalysis' => $eventanalysis_count,
			'riskassessment' => $riskassessment_count,
			'this_week' => $this_week,
			// Keep backward compatibility
			'total_shared' => $total_shared
		);
	}

	/**
	 * Get all shared entities by type
	 */
	public function get_shared_entities_by_type($entity_type, $limit = NULL, $offset = NULL)
	{
		$this->db->select('shares.*');
		$this->db->from($this->db_table('shares'));
		$this->db->where('entity_type', $entity_type);
		$this->db->order_by('shared_date', 'DESC');

		if ($limit !== NULL) {
			$this->db->limit($limit, $offset);
		}

		$query = $this->db->get();
		
		if ($query->num_rows() > 0) {
			$results = $query->result();
			foreach ($results as $result) {
				$result->share_id = BIN_TO_UUID($result->share_id);
				$result->entity_id = BIN_TO_UUID($result->entity_id);
				$result->shared_by = BIN_TO_UUID($result->shared_by);
			}
			return $results;
		}
		
		return array();
	}

	/**
	 * Test method to debug SQL query
	 */
	public function test_query()
	{
		$this->db->select('shares.share_id, documents.name as document_name, users.name as shared_by_name');
		$this->db->from($this->db_table('shares'));
		$this->db->join($this->db_table('documents'), 'documents.document_id = shares.entity_id', 'left');
		$this->db->join($this->db_table('user_table') . ' AS users', 'users.user_id = shares.shared_by', 'left');
		$this->db->where('shares.entity_type', 'document');
		$this->db->limit(1);

		$query = $this->db->get();
		return $query->result();
	}

	/**
	 * Get all shared entities for admin view with entity names
	 */
	public function get_all_shared_entities($limit = NULL, $offset = NULL, $search = NULL)
	{
		$this->db->select('shares.share_id, shares.entity_type, shares.entity_id, shares.shared_by,
			shares.shared_date, users.name as shared_by_name,
			documents.name as document_name,
			"" as deviation_name,
			eventanalysis.name as eventanalysis_name,
			risk_assessments.name as riskassessment_name');

		$this->db->from($this->db_table('shares'));
		$this->db->join($this->db_table('user_table') . ' AS users', 'users.user_id = shares.shared_by', 'left');

		// Join with entity tables to get names
		$this->db->join($this->db_table('documents'), 'documents.document_id = shares.entity_id AND shares.entity_type = "document"', 'left');

		// For deviation, we'll get the name separately after the query since the structure is complex
		$this->db->join($this->db_table('deviation'), 'deviation.a_id = shares.entity_id AND shares.entity_type = "deviation"', 'left');

		$this->db->join($this->db_table('eventanalysis'), 'eventanalysis.id = shares.entity_id AND shares.entity_type = "eventanalysis"', 'left');
		$this->db->join($this->db_table('risk_assessments'), 'risk_assessments.ra_id = shares.entity_id AND shares.entity_type = "riskassessment"', 'left');

		// Search functionality
		if (!empty($search)) {
			$this->db->group_start();
			$this->db->like('shares.entity_type', $search);
			$this->db->or_like('documents.name', $search);
			$this->db->or_like('eventanalysis.name', $search);
			$this->db->or_like('risk_assessments.name', $search);
			$this->db->group_end();
		}

		// Order by shared_date in descending order
		$this->db->order_by('shares.shared_date', 'DESC');

		if ($limit !== NULL) {
			$this->db->limit($limit, $offset);
		}

		$query = $this->db->get();
		
		if ($query->num_rows() > 0) {
			$results = $query->result();

			// Load Deviation_model to get deviation names
			$this->load->model('Deviation_model');

			foreach ($results as $result) {
				$result->share_id = BIN_TO_UUID($result->share_id);
				$result->entity_id = BIN_TO_UUID($result->entity_id);
				$result->shared_by = BIN_TO_UUID($result->shared_by);

				// Get deviation name if it's a deviation
				if ($result->entity_type == 'deviation') {
					$result->deviation_name = $this->get_deviation_name_simple($result->entity_id);
				}

				// Set entity name based on type
				switch($result->entity_type) {
					case 'document':
						$result->entity_name = $result->document_name;
						break;
					case 'deviation':
						$result->entity_name = $result->deviation_name;
						break;
					case 'eventanalysis':
						$result->entity_name = $result->eventanalysis_name;
						break;
					case 'riskassessment':
						$result->entity_name = $result->riskassessment_name;
						break;
					default:
						$result->entity_name = 'Unknown Entity';
				}
			}
			return $results;
		}
		
		return array();
	}

	/**
	 * Get deviation name using a simpler approach
	 */
	public function get_deviation_name_simple($deviation_id)
	{
		// Try to get the deviation name from deviation_answers
		$sql = "SELECT da.answer
				FROM " . $this->db_table('deviation_answers') . " da
				INNER JOIN " . $this->db_table('deviation_fields') . " df ON da.df_id = df.df_id
				WHERE da.a_id = ? AND df.input = 'input' AND df.required_kvalprak = 1
				LIMIT 1";

		$query = $this->db->query($sql, [UUID_TO_BIN($deviation_id)]);

		if ($query && $query->num_rows() > 0) {
			return $query->row()->answer;
		}

		// Fallback: return a generic name with the ID
		return 'Deviation ' . substr($deviation_id, 0, 8);
	}

	/**
	 * Get sharing statistics for all entity types
	 */
	public function get_all_sharing_stats()
	{
		$stats = array();
		
		// Get stats for each entity type
		$entity_types = ['document', 'deviation', 'eventanalysis', 'riskassessment'];
		
		foreach ($entity_types as $type) {
			$this->db->where('entity_type', $type);
			$count = $this->db->count_all_results($this->db_table('shares'));
			$stats[$type] = $count;
		}
		
		// Total shared entities
		$stats['total'] = $this->db->count_all_results($this->db_table('shares'));
		
		// This month
		$this->db->where('shared_date >=', date('Y-m-01 00:00:00'));
		$this->db->where('shared_date <=', date('Y-m-t 23:59:59'));
		$stats['this_month'] = $this->db->count_all_results($this->db_table('shares'));
		
		// This week
		$this->db->where('shared_date >=', date('Y-m-d 00:00:00', strtotime('monday this week')));
		$this->db->where('shared_date <=', date('Y-m-d 23:59:59', strtotime('sunday this week')));
		$stats['this_week'] = $this->db->count_all_results($this->db_table('shares'));

		return $stats;
	}

	/**
	 * Remove entity share by entity type and ID
	 */
	public function remove_entity_share($entity_type, $entity_id)
	{
		return $this->delete_share($entity_type, $entity_id);
	}

	/**
	 * Get share information by share_id
	 */
	public function get_share_by_id($share_id)
	{
		$this->db->select('share_id, entity_type, entity_id, shared_by, shared_date');
		$this->db->from($this->db_table('shares'));
		$this->db->where('share_id', UUID_TO_BIN($share_id));
		
		$query = $this->db->get();
		
		if ($query->num_rows() > 0) {
			$result = $query->row();
			$result->share_id = BIN_TO_UUID($result->share_id);
			$result->entity_id = BIN_TO_UUID($result->entity_id);
			$result->shared_by = BIN_TO_UUID($result->shared_by);
			return $result;
		}
		
		return FALSE;
	}

	/**
	 * Get share_id for a specific entity
	 */
	public function get_share_id($entity_type, $entity_id)
	{
		$this->db->select('share_id');
		$this->db->from($this->db_table('shares'));
		$this->db->where('entity_type', $entity_type);
		$this->db->where('entity_id', UUID_TO_BIN($entity_id));
		
		$query = $this->db->get();
		
		if ($query->num_rows() > 0) {
			$result = $query->row();
			return BIN_TO_UUID($result->share_id);
		}
		
		return FALSE;
	}

	/**
	 * Remove a share by share_id
	 *
	 * @param string $share_id The share_id to remove
	 * @return bool TRUE on success, FALSE on failure
	 */
	public function remove_share_by_id($share_id)
	{
		$this->db->where('share_id', UUID_TO_BIN($share_id));
		return $this->db->delete($this->db_table('shares'));
	}
}
