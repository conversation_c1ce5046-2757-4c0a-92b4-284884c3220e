-- Migration script to convert document_shares to generic shares table
-- This script safely migrates existing data from document_shares to the new shares table

-- First, create the new shares table if it doesn't exist
CREATE TABLE IF NOT EXISTS `shares` (
  `share_id` binary(16) NOT NULL,
  `entity_type` varchar(50) COLLATE utf8mb4_swedish_ci NOT NULL,
  `entity_id` binary(16) NOT NULL,
  `shared_by` binary(16) NOT NULL,
  `shared_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`share_id`),
  UNIQUE KEY `idx_entity_unique` (`entity_type`, `entity_id`),
  KEY `idx_shared_by` (`shared_by`),
  KEY `idx_shared_date` (`shared_date`),
  KEY `idx_entity_type_id` (`entity_type`, `entity_id`),
  CONSTRAINT `fk_shares_user` FOREIGN KEY (`shared_by`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_swedish_ci;

-- Migrate existing document_shares data to the new shares table
-- Only migrate records where is_active = 1 (we're removing the is_active concept)
INSERT IGNORE INTO `shares` (`share_id`, `entity_type`, `entity_id`, `shared_by`, `shared_date`)
SELECT 
    `share_id`,
    'document' as `entity_type`,
    `document_id` as `entity_id`,
    `shared_by`,
    `shared_date`
FROM `document_shares`
WHERE `is_active` = 1;

-- Verify the migration worked correctly
SELECT 
    (SELECT COUNT(*) FROM `document_shares` WHERE `is_active` = 1) as original_count,
    (SELECT COUNT(*) FROM `shares` WHERE `entity_type` = 'document') as migrated_count;

-- After successful migration and testing, you can drop the old table:
-- DROP TABLE IF EXISTS `document_shares`;

-- Update database settings
SET foreign_key_checks = 1;
