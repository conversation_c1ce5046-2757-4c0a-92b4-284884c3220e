-- Database update to add shareable entities feature
-- Create generic table to track shared entities (documents, deviations, event analysis, risk assessments)
CREATE TABLE IF NOT EXISTS `shares` (
  `share_id` binary(16) NOT NULL,
  `entity_type` varchar(50) COLLATE utf8mb4_swedish_ci NOT NULL,
  `entity_id` binary(16) NOT NULL,
  `shared_by` binary(16) NOT NULL,
  `shared_date` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`share_id`),
  UNIQUE KEY `idx_entity_unique` (`entity_type`, `entity_id`),
  <PERSON>EY `idx_shared_by` (`shared_by`),
  KEY `idx_shared_date` (`shared_date`),
  KEY `idx_entity_type_id` (`entity_type`, `entity_id`),
  CONSTRAINT `fk_shares_user` FOREIGN KEY (`shared_by`) REFERENCES `users` (`user_id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_swedish_ci;

-- Update database settings
SET foreign_key_checks = 1;
